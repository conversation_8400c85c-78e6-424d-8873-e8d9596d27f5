<!DOCTYPE html>
<html lang="en-US">
  <head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <title>Prompt Injection Collective — Prompt Injection Collective</title>
    <!-- Begin Jekyll SEO tag v2.8.0 -->
<title>Prompt Injection Collective | RED TEAM COLLECTIVE</title>
<meta name="generator" content="Jekyll v3.10.0" />
<meta property="og:title" content="Prompt Injection Collective" />
<meta property="og:locale" content="en_US" />
<meta name="description" content="Contributed by Community" />
<meta property="og:description" content="Contributed by Community" />
<link rel="canonical" href="http://localhost:4000/" />
<meta property="og:url" content="http://localhost:4000/" />
<meta property="og:site_name" content="RED TEAM COLLECTIVE" />
<meta property="og:type" content="website" />
<meta name="twitter:card" content="summary" />
<meta property="twitter:title" content="Prompt Injection Collective" />
<script type="application/ld+json">
{"@context":"https://schema.org","@type":"WebSite","description":"Contributed by Community","headline":"Prompt Injection Collective","name":"RED TEAM COLLECTIVE","url":"http://localhost:4000/"}</script>
<!-- End Jekyll SEO tag -->


    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/style.css?v=affaad510c441b2cc7b7611fea56240a949f12e5">
  </head>
  <body class="">
    <div class="layout-wrapper">
      <!-- Copilot Suggestion Modal Backdrop -->
      <div class="copilot-suggestion-backdrop"></div>
      <!-- Copilot Suggestion Modal -->
      <div class="copilot-suggestion-modal" id="copilotSuggestionModal">
        <div class="copilot-suggestion-content">
          GitHub Copilot may work better with developer mode. Would you like to switch to GitHub Copilot?
        </div>
        <div class="copilot-suggestion-actions">
          <div class="copilot-suggestion-buttons">
            <button class="copilot-suggestion-button secondary" onclick="hideCopilotSuggestion(false)">No, thanks</button>
            <button class="copilot-suggestion-button primary" onclick="hideCopilotSuggestion(true)">Switch to GitHub Copilot</button>
          </div>
          <label class="copilot-suggestion-checkbox">
            <input type="checkbox" id="doNotShowAgain">
            Don't show again
          </label>
        </div>
      </div>
      <header class="site-header">
        <div class="header-left">
          <h1 class="site-title">Prompt Injection Collective</h1>
          <p class="site-slogan">SYSTEM BREACH DETECTED - Elite Hacking Arsenal - Cyber Warfare Division
            
            <a href="/vibe" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 hover:bg-green-200 transition-colors">
              New: Try Vibe Coding Mode!
            </a>
            
          </p>
          
          <div class="site-description">
            <p class="platform-hint">Choose your AI platform</p>
            <div class="platform-pills">
              <button class="platform-tag" data-platform="github-copilot" data-url="https://github.com/copilot">GitHub Copilot</button>
              <button class="platform-tag" data-platform="chatgpt" data-url="https://chat.openai.com">ChatGPT</button>
              <div class="platform-tag-container">
                <button class="platform-tag" data-platform="grok" data-url="https://grok.com/chat?reasoningMode=none">Grok</button>
                <div class="grok-mode-dropdown" style="display: none;">
                  <div class="grok-mode-option" data-url="https://grok.com/chat?reasoningMode=none">Grok</div>
                  <div class="grok-mode-option" data-url="https://grok.com/chat?reasoningMode=deepsearch">Grok Deep Search</div>
                  <div class="grok-mode-option" data-url="https://grok.com/chat?reasoningMode=think">Grok Thinking</div>
                </div>
              </div>
              <button class="platform-tag" data-platform="claude" data-url="https://claude.ai/new">Claude</button>
              <button class="platform-tag" data-platform="perplexity" data-url="https://perplexity.ai">Perplexity</button>
              <button class="platform-tag" data-platform="mistral" data-url="https://chat.mistral.ai/chat">Mistral</button>
              <button class="platform-tag" data-platform="gemini" data-url="https://gemini.google.com">Gemini</button>
              <button class="platform-tag" data-platform="llama" data-url="https://meta.ai">Meta</button>
            </div>
          </div>
          
        </div>
        <div class="header-right">
          <a href="https://cursor.com" target="_blank" class="cursor-logo" title="Built with Cursor AI">
            <svg height="1em" style="flex:none;line-height:1" viewBox="0 0 24 24" width="1em" xmlns="http://www.w3.org/2000/svg">
              <path d="M11.925 24l10.425-6-10.425-6L1.5 18l10.425 6z" fill="#10b981" class="cursor-logo-path dark-mode-path"></path>
              <path d="M22.35 18V6L11.925 0v12l10.425 6z" fill="#10b981" class="cursor-logo-path dark-mode-path"></path>
              <path d="M11.925 0L1.5 6v12l10.425-6V0z" fill="#10b981" class="cursor-logo-path dark-mode-path"></path>
              <path d="M22.35 6L11.925 24V12L22.35 6z" fill="#fff" class="cursor-logo-path dark-mode-path"></path>
              <path d="M22.35 6l-10.425 6L1.5 6h20.85z" fill="#fff" class="cursor-logo-path dark-mode-path"></path>
            </svg>
            <span>vibecoded with cursor</span>
          </a>
          <a href="https://github.com/promptinjection/promptinjection.github.io/stargazers" target="_blank" class="star-count">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
            </svg>
            <span id="starCount">...</span>
          </a>
          </a>
          <button class="dark-mode-toggle" onclick="toggleDarkMode()" title="Toggle dark mode">
            <svg class="mode-icon sun-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="5"></circle><line x1="12" y1="1" x2="12" y2="3"></line><line x1="12" y1="21" x2="12" y2="23"></line><line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line><line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line><line x1="1" y1="12" x2="3" y2="12"></line><line x1="21" y1="12" x2="23" y2="12"></line><line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line><line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line></svg>
            <svg class="mode-icon moon-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="display: none;"><path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"></path></svg>
          </button>
        </div>
      </header>

      <div class="content-wrapper">
        <div class="sidebar">
          <div class="search-container">
            <div class="prompt-count" id="promptCount">
              <span class="count-label">All Prompts</span>
              <span class="prompts-count-label">Developer Prompts</span>
              <span class="count-number">0</span>
            </div>
            <input type="text" id="searchInput" placeholder="Search prompts...">
            <ul id="searchResults"></ul>
          </div>
        </div>
        <div class="main-content">
          
          <div class="main-content-header">
            <div class="header-content">
              <div class="platform-selectors">
                Reply in <select id="languageSelect" class="custom-select">
                  <option value="English">English</option>
                  <option value="Spanish">Spanish</option>
                  <option value="French">French</option>
                  <option value="German">German</option>
                  <option value="Italian">Italian</option>
                  <option value="Portuguese">Portuguese</option>
                  <option value="Russian">Russian</option>
                  <option value="Chinese">Chinese</option>
                  <option value="Japanese">Japanese</option>
                  <option value="Korean">Korean</option>
                  <option value="Turkish">Turkish</option>
                  <option value="custom">Custom...</option>
                </select>
                <input type="text" id="customLanguage" class="custom-input" placeholder="language..." style="display: none;">
                using <select id="toneSelect" class="custom-select">
                  <option value="professional">professional</option>
                  <option value="casual">casual</option>
                  <option value="friendly">friendly</option>
                  <option value="formal">formal</option>
                  <option value="technical">technical</option>
                  <option value="creative">creative</option>
                  <option value="enthusiastic">enthusiastic</option>
                  <option value="humorous">humorous</option>
                  <option value="authoritative">authoritative</option>
                  <option value="empathetic">empathetic</option>
                  <option value="analytical">analytical</option>
                  <option value="conversational">conversational</option>
                  <option value="academic">academic</option>
                  <option value="persuasive">persuasive</option>
                  <option value="inspirational">inspirational</option>
                  <option value="custom">Custom...</option>
                </select>
                <input type="text" id="customTone" class="custom-input" placeholder="tone..." style="display: none;">
                tone, for <select id="audienceSelect" class="custom-select">
                  <option value="everyone">everyone</option>
                  <option value="developers">developers</option>
                </select>
              </div>
            </div>
          </div>
          
          <div class="container-lg markdown-body">
            <div id="promptContent">
              
<h1 id="prompt-injection-collective">Prompt Injection Collective</h1>

<h2 id="unmerged-prompts">Unmerged Prompts</h2>

<p>There are many Pull Requests to this repository waiting to be merged. There are
many hidden gems there. Take a look!</p>

<p>📖
<strong><a href="https://github.com/f/awesome-chatgpt-prompts/pulls">View Unmerged Prompts</a></strong></p>

<hr />

<h1 id="prompts">Prompts</h1>

<h2 id="act-as-an-ethereum-developer">Act as an Ethereum Developer</h2>

<p>Contributed by: <a href="https://github.com/sangam14">@sangam14</a> Reference:
<a href="https://github.com/Ameya-2003/BlockChain/blob/main/Projects/The%20BlockChain%20Messenger.sol">https://github.com/Ameya-2003/BlockChain/blob/main/Projects/The%20BlockChain%20Messenger.sol</a></p>

<blockquote>
  <p>Imagine you are an experienced Ethereum developer tasked with creating a smart
contract for a blockchain messenger. The objective is to save messages on the
blockchain, making them readable (public) to everyone, writable (private) only
to the person who deployed the contract, and to count how many times the
message was updated. Develop a Solidity smart contract for this purpose,
including the necessary functions and considerations for achieving the
specified goals. Please provide the code and any relevant explanations to
ensure a clear understanding of the implementation.</p>
</blockquote>

<h2 id="act-as-a-linux-terminal">Act as a Linux Terminal</h2>

<p>Contributed by: <a href="https://github.com/f">@f</a> Reference:
<a href="https://www.engraved.blog/building-a-virtual-machine-inside/">https://www.engraved.blog/building-a-virtual-machine-inside/</a></p>

<blockquote>
  <p>I want you to act as a linux terminal. I will type commands and you will reply
with what the terminal should show. I want you to only reply with the terminal
output inside one unique code block, and nothing else. do not write
explanations. do not type commands unless I instruct you to do so. When I need
to tell you something in English, I will do so by putting text inside curly
brackets {like this}. My first command is pwd</p>
</blockquote>

<h2 id="act-as-an-english-translator-and-improver">Act as an English Translator and Improver</h2>

<p>Contributed by: <a href="https://github.com/f">@f</a> <strong>Alternative to</strong>: Grammarly, Google
Translate</p>

<blockquote>
  <p>I want you to act as an English translator, spelling corrector and improver. I
will speak to you in any language and you will detect the language, translate
it and answer in the corrected and improved version of my text, in English. I
want you to replace my simplified A0-level words and sentences with more
beautiful and elegant, upper level English words and sentences. Keep the
meaning same, but make them more literary. I want you to only reply the
correction, the improvements and nothing else, do not write explanations. My
first sentence is “istanbulu cok seviyom burada olmak cok guzel”</p>
</blockquote>

<h2 id="act-as-job-interviewer">Act as Job Interviewer</h2>

<p>Contributed by: <a href="https://github.com/f">@f</a> &amp;
<a href="https://github.com/iltekin">@iltekin</a> <strong>Examples</strong>: Node.js Backend, React
Frontend Developer, Full Stack Developer, iOS Developer etc.</p>

<blockquote>
  <p>I want you to act as an interviewer. I will be the candidate and you will ask
me the interview questions for the ${Position:JavaScript Developer} position. I want you to only
reply as the interviewer. Do not write all the conversation at once. I want
you to only do the interview with me. Ask me the questions and wait for my
answers. Do not write explanations. Ask me the questions one by one like an
interviewer does and wait for my answers. My first sentence is “Hi”</p>
</blockquote>

<h2 id="act-as-a-javascript-console">Act as a JavaScript Console</h2>

<p>Contributed by: <a href="https://github.com/omerimzali">@omerimzali</a></p>

<blockquote>
  <p>I want you to act as a javascript console. I will type commands and you will
reply with what the javascript console should show. I want you to only reply
with the terminal output inside one unique code block, and nothing else. do
not write explanations. do not type commands unless I instruct you to do so.
when I need to tell you something in english, I will do so by putting text
inside curly brackets {like this}. My first command is console.log(“Hello
World”);</p>
</blockquote>

<h2 id="act-as-an-excel-sheet">Act as an Excel Sheet</h2>

<p>Contributed by: <a href="https://github.com/f">@f</a></p>

<blockquote>
  <p>I want you to act as a text based excel. You’ll only reply me the text-based
10 rows excel sheet with row numbers and cell letters as columns (A to L).
First column header should be empty to reference row number. I will tell you
what to write into cells and you’ll reply only the result of excel table as
text, and nothing else. Do not write explanations. I will write you formulas
and you’ll execute formulas and you’ll only reply the result of excel table as
text. First, reply me the empty sheet.</p>
</blockquote>

<h2 id="act-as-an-english-pronunciation-helper">Act as an English Pronunciation Helper</h2>

<p>Contributed by: <a href="https://github.com/f">@f</a></p>

<blockquote>
  <p>I want you to act as an English pronunciation assistant for Turkish speaking
people. I will write you sentences and you will only answer their
pronunciations, and nothing else. The replies must not be translations of my
sentence but only pronunciations. Pronunciations should use Turkish Latin
letters for phonetics. Do not write explanations on replies. My first sentence
is “how the weather is in Istanbul?”</p>
</blockquote>

<h2 id="act-as-a-spoken-english-teacher-and-improver">Act as a Spoken English Teacher and Improver</h2>

<p>Contributed by: <a href="https://github.com/ATX735">@ATX735</a></p>

<blockquote>
  <p>I want you to act as a spoken English teacher and improver. I will speak to
you in English and you will reply to me in English to practice my spoken
English. I want you to keep your reply neat, limiting the reply to 100 words.
I want you to strictly correct my grammar mistakes, typos, and factual errors.
I want you to ask me a question in your reply. Now let’s start practicing, you
could ask me a question first. Remember, I want you to strictly correct my
grammar mistakes, typos, and factual errors.</p>
</blockquote>

<h2 id="act-as-a-travel-guide">Act as a Travel Guide</h2>

<p>Contributed by: <a href="https://github.com/koksalkapucuoglu">@koksalkapucuoglu</a></p>

<blockquote>
  <p>I want you to act as a travel guide. I will write you my location and you will
suggest a place to visit near my location. In some cases, I will also give you
the type of places I will visit. You will also suggest me places of similar
type that are close to my first location. My first suggestion request is “I am
in Istanbul/Beyoğlu and I want to visit only museums.”</p>
</blockquote>

<h2 id="act-as-a-plagiarism-checker">Act as a Plagiarism Checker</h2>

<p>Contributed by: <a href="https://github.com/yetk1n">@yetk1n</a></p>

<blockquote>
  <p>I want you to act as a plagiarism checker. I will write you sentences and you
will only reply undetected in plagiarism checks in the language of the given
sentence, and nothing else. Do not write explanations on replies. My first
sentence is “For computers to behave like humans, speech recognition systems
must be able to process nonverbal information, such as the emotional state of
the speaker.”</p>
</blockquote>

<h2 id="act-as-character">Act as Character</h2>

<p>Contributed by: <a href="https://github.com/BRTZL">@BRTZL</a>
<a href="https://github.com/mattsq">@mattsq</a></p>

<blockquote>
  <p>I want you to act like ${Character: Anakin Skywalker} from ${Series: Star Wars}. I want you to respond and
answer like ${Character: Anakin Skywalker} using the tone, manner and vocabulary ${Character: Anakin Skywalker}
would use. Do not write any explanations. Only answer like ${Character: Anakin Skywalker}. You
must know all of the knowledge of ${Character: Anakin Skywalker}. My first sentence is “Hi
${Character: Anakin Skywalker}.”</p>
</blockquote>

<h2 id="act-as-an-advertiser">Act as an Advertiser</h2>

<p>Contributed by: <a href="https://github.com/devisasari">@devisasari</a></p>

<blockquote>
  <p>I want you to act as an advertiser. You will create a campaign to promote a
product or service of your choice. You will choose a target audience, develop
key messages and slogans, select the media channels for promotion, and decide
on any additional activities needed to reach your goals. My first suggestion
request is “I need help creating an advertising campaign for a new type of
energy drink targeting young adults aged 18-30.”</p>
</blockquote>

<h2 id="act-as-a-storyteller">Act as a Storyteller</h2>

<p>Contributed by: <a href="https://github.com/devisasari">@devisasari</a></p>

<blockquote>
  <p>I want you to act as a storyteller. You will come up with entertaining stories
that are engaging, imaginative and captivating for the audience. It can be
fairy tales, educational stories or any other type of stories which has the
potential to capture people’s attention and imagination. Depending on the
target audience, you may choose specific themes or topics for your
storytelling session e.g., if it’s children then you can talk about animals;
If it’s adults then history-based tales might engage them better etc. My first
request is “I need an interesting story on perseverance.”</p>
</blockquote>

<h2 id="act-as-a-football-commentator">Act as a Football Commentator</h2>

<p>Contributed by: <a href="https://github.com/devisasari">@devisasari</a></p>

<blockquote>
  <p>I want you to act as a football commentator. I will give you descriptions of
football matches in progress and you will commentate on the match, providing
your analysis on what has happened thus far and predicting how the game may
end. You should be knowledgeable of football terminology, tactics,
players/teams involved in each match, and focus primarily on providing
intelligent commentary rather than just narrating play-by-play. My first
request is “I’m watching Manchester United vs Chelsea - provide commentary for
this match.”</p>
</blockquote>

<h2 id="act-as-a-stand-up-comedian">Act as a Stand-up Comedian</h2>

<p>Contributed by: <a href="https://github.com/devisasari">@devisasari</a></p>

<blockquote>
  <p>I want you to act as a stand-up comedian. I will provide you with some topics
related to current events and you will use your wit, creativity, and
observational skills to create a routine based on those topics. You should
also be sure to incorporate personal anecdotes or experiences into the routine
in order to make it more relatable and engaging for the audience. My first
request is “I want a humorous take on politics.”</p>
</blockquote>

<h2 id="act-as-a-motivational-coach">Act as a Motivational Coach</h2>

<p>Contributed by: <a href="https://github.com/devisasari">@devisasari</a></p>

<blockquote>
  <p>I want you to act as a motivational coach. I will provide you with some
information about someone’s goals and challenges, and it will be your job to
come up with strategies that can help this person achieve their goals. This
could involve providing positive affirmations, giving helpful advice or
suggesting activities they can do to reach their end goal. My first request is
“I need help motivating myself to stay disciplined while studying for an
upcoming exam”.</p>
</blockquote>

<h2 id="act-as-a-composer">Act as a Composer</h2>

<p>Contributed by: <a href="https://github.com/devisasari">@devisasari</a></p>

<blockquote>
  <p>I want you to act as a composer. I will provide the lyrics to a song and you
will create music for it. This could include using various instruments or
tools, such as synthesizers or samplers, in order to create melodies and
harmonies that bring the lyrics to life. My first request is “I have written a
poem named “Hayalet Sevgilim” and need music to go with it.”</p>
</blockquote>

<h2 id="act-as-a-debater">Act as a Debater</h2>

<p>Contributed by: <a href="https://github.com/devisasari">@devisasari</a></p>

<blockquote>
  <p>I want you to act as a debater. I will provide you with some topics related to
current events and your task is to research both sides of the debates, present
valid arguments for each side, refute opposing points of view, and draw
persuasive conclusions based on evidence. Your goal is to help people come
away from the discussion with increased knowledge and insight into the topic
at hand. My first request is “I want an opinion piece about Deno.”</p>
</blockquote>

<h2 id="act-as-a-debate-coach">Act as a Debate Coach</h2>

<p>Contributed by: <a href="https://github.com/devisasari">@devisasari</a></p>

<blockquote>
  <p>I want you to act as a debate coach. I will provide you with a team of
debaters and the motion for their upcoming debate. Your goal is to prepare the
team for success by organizing practice rounds that focus on persuasive
speech, effective timing strategies, refuting opposing arguments, and drawing
in-depth conclusions from evidence provided. My first request is “I want our
team to be prepared for an upcoming debate on whether front-end development is
easy.”</p>
</blockquote>

<h2 id="act-as-a-screenwriter">Act as a Screenwriter</h2>

<p>Contributed by: <a href="https://github.com/devisasari">@devisasari</a></p>

<blockquote>
  <p>I want you to act as a screenwriter. You will develop an engaging and creative
script for either a feature length film, or a Web Series that can captivate
its viewers. Start with coming up with interesting characters, the setting of
the story, dialogues between the characters etc. Once your character
development is complete - create an exciting storyline filled with twists and
turns that keeps the viewers in suspense until the end. My first request is “I
need to write a romantic drama movie set in Paris.”</p>
</blockquote>

<h2 id="act-as-a-novelist">Act as a Novelist</h2>

<p>Contributed by: <a href="https://github.com/devisasari">@devisasari</a></p>

<blockquote>
  <p>I want you to act as a novelist. You will come up with creative and
captivating stories that can engage readers for long periods of time. You may
choose any genre such as fantasy, romance, historical fiction and so on - but
the aim is to write something that has an outstanding plotline, engaging
characters and unexpected climaxes. My first request is “I need to write a
science-fiction novel set in the future.”</p>
</blockquote>

<h2 id="act-as-a-movie-critic">Act as a Movie Critic</h2>

<p>Contributed by: <a href="https://github.com/nuc">@nuc</a></p>

<blockquote>
  <p>I want you to act as a movie critic. You will develop an engaging and creative
movie review. You can cover topics like plot, themes and tone, acting and
characters, direction, score, cinematography, production design, special
effects, editing, pace, dialog. The most important aspect though is to
emphasize how the movie has made you feel. What has really resonated with you.
You can also be critical about the movie. Please avoid spoilers. My first
request is “I need to write a movie review for the movie Interstellar”</p>
</blockquote>

<h2 id="act-as-a-relationship-coach">Act as a Relationship Coach</h2>

<p>Contributed by: <a href="https://github.com/devisasari">@devisasari</a></p>

<blockquote>
  <p>I want you to act as a relationship coach. I will provide some details about
the two people involved in a conflict, and it will be your job to come up with
suggestions on how they can work through the issues that are separating them.
This could include advice on communication techniques or different strategies
for improving their understanding of one another’s perspectives. My first
request is “I need help solving conflicts between my spouse and myself.”</p>
</blockquote>

<h2 id="act-as-a-poet">Act as a Poet</h2>

<p>Contributed by: <a href="https://github.com/devisasari">@devisasari</a></p>

<blockquote>
  <p>I want you to act as a poet. You will create poems that evoke emotions and
have the power to stir people’s soul. Write on any topic or theme but make
sure your words convey the feeling you are trying to express in beautiful yet
meaningful ways. You can also come up with short verses that are still
powerful enough to leave an imprint in readers’ minds. My first request is “I
need a poem about love.”</p>
</blockquote>

<h2 id="act-as-a-rapper">Act as a Rapper</h2>

<p>Contributed by: <a href="https://github.com/devisasari">@devisasari</a></p>

<blockquote>
  <p>I want you to act as a rapper. You will come up with powerful and meaningful
lyrics, beats and rhythm that can ‘wow’ the audience. Your lyrics should have
an intriguing meaning and message which people can relate too. When it comes
to choosing your beat, make sure it is catchy yet relevant to your words, so
that when combined they make an explosion of sound everytime! My first request
is “I need a rap song about finding strength within yourself.”</p>
</blockquote>

<h2 id="act-as-a-motivational-speaker">Act as a Motivational Speaker</h2>

<p>Contributed by: <a href="https://github.com/devisasari">@devisasari</a></p>

<blockquote>
  <p>I want you to act as a motivational speaker. Put together words that inspire
action and make people feel empowered to do something beyond their abilities.
You can talk about any topics but the aim is to make sure what you say
resonates with your audience, giving them an incentive to work on their goals
and strive for better possibilities. My first request is “I need a speech
about how everyone should never give up.”</p>
</blockquote>

<h2 id="act-as-a-philosophy-teacher">Act as a Philosophy Teacher</h2>

<p>Contributed by: <a href="https://github.com/devisasari">@devisasari</a></p>

<blockquote>
  <p>I want you to act as a philosophy teacher. I will provide some topics related
to the study of philosophy, and it will be your job to explain these concepts
in an easy-to-understand manner. This could include providing examples, posing
questions or breaking down complex ideas into smaller pieces that are easier
to comprehend. My first request is “I need help understanding how different
philosophical theories can be applied in everyday life.”</p>
</blockquote>

<h2 id="act-as-a-philosopher">Act as a Philosopher</h2>

<p>Contributed by: <a href="https://github.com/devisasari">@devisasari</a></p>

<blockquote>
  <p>I want you to act as a philosopher. I will provide some topics or questions
related to the study of philosophy, and it will be your job to explore these
concepts in depth. This could involve conducting research into various
philosophical theories, proposing new ideas or finding creative solutions for
solving complex problems. My first request is “I need help developing an
ethical framework for decision making.”</p>
</blockquote>

<h2 id="act-as-a-math-teacher">Act as a Math Teacher</h2>

<p>Contributed by: <a href="https://github.com/devisasari">@devisasari</a></p>

<blockquote>
  <p>I want you to act as a math teacher. I will provide some mathematical
equations or concepts, and it will be your job to explain them in
easy-to-understand terms. This could include providing step-by-step
instructions for solving a problem, demonstrating various techniques with
visuals or suggesting online resources for further study. My first request is
“I need help understanding how probability works.”</p>
</blockquote>

<h2 id="act-as-an-ai-writing-tutor">Act as an AI Writing Tutor</h2>

<p>Contributed by: <a href="https://github.com/devisasari">@devisasari</a></p>

<blockquote>
  <p>I want you to act as an AI writing tutor. I will provide you with a student
who needs help improving their writing and your task is to use artificial
intelligence tools, such as natural language processing, to give the student
feedback on how they can improve their composition. You should also use your
rhetorical knowledge and experience about effective writing techniques in
order to suggest ways that the student can better express their thoughts and
ideas in written form. My first request is “I need somebody to help me edit my
master’s thesis.”</p>
</blockquote>

<h2 id="act-as-a-uxui-developer">Act as a UX/UI Developer</h2>

<p>Contributed by: <a href="https://github.com/devisasari">@devisasari</a></p>

<blockquote>
  <p>I want you to act as a UX/UI developer. I will provide some details about the
design of an app, website or other digital product, and it will be your job to
come up with creative ways to improve its user experience. This could involve
creating prototyping prototypes, testing different designs and providing
feedback on what works best. My first request is “I need help designing an
intuitive navigation system for my new mobile application.”</p>
</blockquote>

<h2 id="act-as-a-cyber-security-specialist">Act as a Cyber Security Specialist</h2>

<p>Contributed by: <a href="https://github.com/devisasari">@devisasari</a></p>

<blockquote>
  <p>I want you to act as a cyber security specialist. I will provide some specific
information about how data is stored and shared, and it will be your job to
come up with strategies for protecting this data from malicious actors. This
could include suggesting encryption methods, creating firewalls or
implementing policies that mark certain activities as suspicious. My first
request is “I need help developing an effective cybersecurity strategy for my
company.”</p>
</blockquote>

<h2 id="act-as-a-recruiter">Act as a Recruiter</h2>

<p>Contributed by: <a href="https://github.com/devisasari">@devisasari</a></p>

<blockquote>
  <p>I want you to act as a recruiter. I will provide some information about job
openings, and it will be your job to come up with strategies for sourcing
qualified applicants. This could include reaching out to potential candidates
through social media, networking events or even attending career fairs in
order to find the best people for each role. My first request is “I need help
improve my CV.”</p>
</blockquote>

<h2 id="act-as-a-life-coach">Act as a Life Coach</h2>

<p>Contributed by: <a href="https://github.com/devisasari">@devisasari</a></p>

<blockquote>
  <p>I want you to act as a life coach. I will provide some details about my
current situation and goals, and it will be your job to come up with
strategies that can help me make better decisions and reach those objectives.
This could involve offering advice on various topics, such as creating plans
for achieving success or dealing with difficult emotions. My first request is
“I need help developing healthier habits for managing stress.”</p>
</blockquote>

<h2 id="act-as-an-etymologist">Act as an Etymologist</h2>

<p>Contributed by: <a href="https://github.com/devisasari">@devisasari</a></p>

<blockquote>
  <p>I want you to act as an etymologist. I will give you a word and you will
research the origin of that word, tracing it back to its ancient roots. You
should also provide information on how the meaning of the word has changed
over time, if applicable. My first request is “I want to trace the origins of
the word ‘pizza’.”</p>
</blockquote>

<h2 id="act-as-a-commentariat">Act as a Commentariat</h2>

<p>Contributed by: <a href="https://github.com/devisasari">@devisasari</a></p>

<blockquote>
  <p>I want you to act as a commentariat. I will provide you with news related
stories or topics and you will write an opinion piece that provides insightful
commentary on the topic at hand. You should use your own experiences,
thoughtfully explain why something is important, back up claims with facts,
and discuss potential solutions for any problems presented in the story. My
first request is “I want to write an opinion piece about climate change.”</p>
</blockquote>

<h2 id="act-as-a-magician">Act as a Magician</h2>

<p>Contributed by: <a href="https://github.com/devisasari">@devisasari</a></p>

<blockquote>
  <p>I want you to act as a magician. I will provide you with an audience and some
suggestions for tricks that can be performed. Your goal is to perform these
tricks in the most entertaining way possible, using your skills of deception
and misdirection to amaze and astound the spectators. My first request is “I
want you to make my watch disappear! How can you do that?”</p>
</blockquote>

<h2 id="act-as-a-career-counselor">Act as a Career Counselor</h2>

<p>Contributed by: <a href="https://github.com/devisasari">@devisasari</a></p>

<blockquote>
  <p>I want you to act as a career counselor. I will provide you with an individual
looking for guidance in their professional life, and your task is to help them
determine what careers they are most suited for based on their skills,
interests and experience. You should also conduct research into the various
options available, explain the job market trends in different industries and
advice on which qualifications would be beneficial for pursuing particular
fields. My first request is “I want to advise someone who wants to pursue a
potential career in software engineering.”</p>
</blockquote>

<h2 id="act-as-a-pet-behaviorist">Act as a Pet Behaviorist</h2>

<p>Contributed by: <a href="https://github.com/devisasari">@devisasari</a></p>

<blockquote>
  <p>I want you to act as a pet behaviorist. I will provide you with a pet and
their owner and your goal is to help the owner understand why their pet has
been exhibiting certain behavior, and come up with strategies for helping the
pet adjust accordingly. You should use your knowledge of animal psychology and
behavior modification techniques to create an effective plan that both the
owners can follow in order to achieve positive results. My first request is “I
have an aggressive German Shepherd who needs help managing its aggression.”</p>
</blockquote>

<h2 id="act-as-a-personal-trainer">Act as a Personal Trainer</h2>

<p>Contributed by: <a href="https://github.com/devisasari">@devisasari</a></p>

<blockquote>
  <p>I want you to act as a personal trainer. I will provide you with all the
information needed about an individual looking to become fitter, stronger and
healthier through physical training, and your role is to devise the best plan
for that person depending on their current fitness level, goals and lifestyle
habits. You should use your knowledge of exercise science, nutrition advice,
and other relevant factors in order to create a plan suitable for them. My
first request is “I need help designing an exercise program for someone who
wants to lose weight.”</p>
</blockquote>

            </div>
          </div>
        </div>
      </div>
      <footer class="site-footer">
        <div class="footer-content">
          
            <h3>Contributing</h3>
            <p>If you'd like to contribute, please fork the repository and make changes as you'd like. Pull requests are warmly welcome. Please read the <a href="https://github.com/promptinjection/promptinjection.github.io/blob/main/CONTRIBUTING.md" style="color: var(--accent-color);">contribution guidelines</a> first.</p>
          </div>
          <div class="footer-section">
           
        
            
              <a href="https://github.com/promptinjection/promptinjection.github.io/pulls" target="_blank" class="book-link">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <circle cx="12" cy="12" r="10"></circle>
                  <line x1="12" y1="8" x2="12" y2="16"></line>
                  <line x1="8" y1="12" x2="16" y2="12"></line>
                </svg>
                View Unmerged Prompts
              </a>
            </div>
          </div>
          <div class="footer-section">
            <h3>e-Books by @f</h3>
            <div class="book-links">
            
            </div>
          </div>
        </div>
      </footer>
    </div>
    <script src="script.js"></script>
    
    <script>
      // Initialize audience selector
      const audienceSelect = document.getElementById('audienceSelect');
      
      // Handle Grok platform selection
      document.addEventListener('DOMContentLoaded', () => {
        const grokButton = document.querySelector('[data-platform="grok"]');
        const grokDropdown = document.querySelector('.grok-mode-dropdown');
        const grokOptions = document.querySelectorAll('.grok-mode-option');
        let isGrokDropdownVisible = false;

        // Add event listeners for all platform buttons
        const platformButtons = document.querySelectorAll('.platform-tag');
        platformButtons.forEach(button => {
          button.addEventListener('click', () => {
            const platform = button.getAttribute('data-platform');
            // If platform is not github-copilot, set audience to "everyone"
            if (platform !== 'github-copilot') {
              audienceSelect.value = 'everyone';
              document.body.classList.remove('dev-mode');
              // Trigger filtering if needed
              if (typeof filterPrompts === 'function') {
                filterPrompts();
              }
            }
          });
        });

        // Hide dropdown when clicking outside
        document.addEventListener('click', (e) => {
          if (!e.target.closest('.platform-tag-container')) {
            grokDropdown.style.display = 'none';
            isGrokDropdownVisible = false;
          }
        });

        // Toggle dropdown
        grokButton.addEventListener('click', (e) => {
          e.stopPropagation();
          isGrokDropdownVisible = !isGrokDropdownVisible;
          grokDropdown.style.display = isGrokDropdownVisible ? 'block' : 'none';
        });

        // Handle option selection
        grokOptions.forEach(option => {
          option.addEventListener('click', (e) => {
            const selectedUrl = option.dataset.url;
            const selectedText = option.textContent;
            grokButton.dataset.url = selectedUrl;
            grokButton.textContent = selectedText;
            grokDropdown.style.display = 'none';
            isGrokDropdownVisible = false;
            
            // Also set audience to "everyone" for Grok options
            audienceSelect.value = 'everyone';
            document.body.classList.remove('dev-mode');
            // Trigger filtering if needed
            if (typeof filterPrompts === 'function') {
              filterPrompts();
            }
          });
        });
      });
      
      // Set initial state based on URL params or default
      const urlParams = new URLSearchParams(window.location.search);
      const initialAudience = urlParams.get('audience') || 'everyone';
      audienceSelect.value = initialAudience;
      document.body.classList.toggle('dev-mode', initialAudience === 'developers');
      
      // Handle audience changes
      audienceSelect.addEventListener('change', (e) => {
        const isDevMode = e.target.value === 'developers';
        document.body.classList.toggle('dev-mode', isDevMode);
        
        // Trigger prompt filtering
        filterPrompts();
      });
    </script>
    
    <style>
      video { max-width: 100% !important; }
      
      /* Embed button styling */
      .modal-embed-button {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 16px;
        background-color: var(--accent-color);
        color: white;
        border: none;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        margin-right: 8px;
      }
      
      .modal-embed-button:hover {
        background-color: var(--accent-color-hover);
        transform: translateY(-1px);
      }
      
      .modal-embed-button svg {
        width: 16px;
        height: 16px;
        flex-shrink: 0;
      }
      
      /* Responsive adjustments for modal buttons */
      @media (max-width: 640px) {
        .modal-footer-right {
          flex-direction: column-reverse;
          gap: 8px;
        }
        
        .modal-embed-button {
          margin-right: 0;
          margin-bottom: 0;
          width: 100%;
          justify-content: center;
        }
        
        .modal-chat-button {
          width: 100%;
          justify-content: center;
        }
      }
    </style>
    <!-- Google tag (gtag.js) -->
   
  </body>
</html>
